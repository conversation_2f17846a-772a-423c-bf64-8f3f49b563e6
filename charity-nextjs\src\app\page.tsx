import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-primary">Charity Info</h1>
            <nav className="hidden md:flex space-x-6">
              <a href="#" className="text-foreground hover:text-primary">Home</a>
              <a href="#" className="text-foreground hover:text-primary">About</a>
              <a href="#" className="text-foreground hover:text-primary">News</a>
              <a href="#" className="text-foreground hover:text-primary">Contact</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-foreground mb-4">
            Welcome to Charity Information Website
          </h2>
          <p className="text-xl text-muted-foreground mb-8">
            Supporting communities through charitable work and transparency
          </p>
          <div className="flex gap-4 justify-center">
            <Button variant="default" size="lg">
              Learn More
            </Button>
            <Button variant="outline" size="lg">
              Get Involved
            </Button>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardHeader>
              <CardTitle>Our Mission</CardTitle>
              <CardDescription>
                Dedicated to making a positive impact in communities worldwide
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                We work tirelessly to support those in need through various charitable initiatives and programs.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Transparency</CardTitle>
              <CardDescription>
                Open and honest about how donations are used
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Every donation is tracked and reported to ensure maximum impact and accountability.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Community</CardTitle>
              <CardDescription>
                Building stronger communities together
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Join our community of volunteers and supporters making a difference every day.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Migration Status */}
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader>
            <CardTitle className="text-primary">🚀 Next.js Migration in Progress</CardTitle>
            <CardDescription>
              Successfully migrated to Next.js with improved performance and SEO
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-green-600">✅</span>
                <span className="text-sm">Next.js project setup complete</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-600">✅</span>
                <span className="text-sm">UI components migrated</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-600">✅</span>
                <span className="text-sm">Tailwind CSS configured</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-yellow-600">🔄</span>
                <span className="text-sm">Page migration in progress</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>

      {/* Footer */}
      <footer className="border-t bg-card mt-12">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-muted-foreground">
            <p>&copy; 2024 Charity Information Website. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
