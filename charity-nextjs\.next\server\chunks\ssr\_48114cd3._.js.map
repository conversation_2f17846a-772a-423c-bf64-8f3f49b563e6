{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/charity_info/charity_info/charity-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Parse and format tags in news content\n * Converts text in the format \"**Tags:** tag1, tag2, tag3\" to HTML tags\n * @param content - The HTML content to parse\n * @returns Formatted HTML content with tags\n */\nexport function parseNewsContent(content: string): string {\n  // Regular expression to match tag format: **Tags:** tag1, tag2, tag3\n  const tagRegex = /\\*\\*Tags:\\*\\*\\s*([^<]+)(?=<\\/p>|$)/g;\n\n  // Replace matched tag sections with formatted tags\n  return content.replace(tagRegex, (match, tagList) => {\n    // Split the tag list by commas and trim whitespace\n    const tags = tagList.split(',').map(tag => tag.trim()).filter(Boolean);\n\n    if (tags.length === 0) {\n      return match; // No valid tags found, return original text\n    }\n\n    // Create HTML for tags\n    const tagsHtml = tags.map(tag =>\n      `<span class=\"inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2\">${tag}</span>`\n    ).join('');\n\n    return `<div class=\"mt-4\"><span class=\"font-semibold\">Tags:</span> <div class=\"flex flex-wrap mt-1\">${tagsHtml}</div></div>`;\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,iBAAiB,OAAe;IAC9C,qEAAqE;IACrE,MAAM,WAAW;IAEjB,mDAAmD;IACnD,OAAO,QAAQ,OAAO,CAAC,UAAU,CAAC,OAAO;QACvC,mDAAmD;QACnD,MAAM,OAAO,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC;QAE9D,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,OAAO,OAAO,4CAA4C;QAC5D;QAEA,uBAAuB;QACvB,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA,MACxB,CAAC,0HAA0H,EAAE,IAAI,OAAO,CAAC,EACzI,IAAI,CAAC;QAEP,OAAO,CAAC,4FAA4F,EAAE,SAAS,YAAY,CAAC;IAC9H;AACF", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/charity_info/charity_info/charity-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-charity-primary text-white hover:bg-charity-secondary\",\n        destructive:\n          \"bg-charity-destructive text-white hover:bg-charity-destructive/90\",\n        outline:\n          \"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-charity-light text-charity-dark hover:bg-charity-light/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-charity-primary underline-offset-4 hover:underline\",\n        accent: \"bg-charity-accent text-charity-dark hover:bg-charity-accent/80\",\n        success: \"bg-charity-success text-white hover:bg-charity-success/90\",\n        warning: \"bg-charity-warning text-white hover:bg-charity-warning/90\",\n        info: \"bg-charity-info text-white hover:bg-charity-info/90\",\n        donate: \"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3 py-1.5 text-xs\",\n        lg: \"h-11 rounded-md px-8 py-2.5\",\n        xl: \"h-12 rounded-md px-10 py-3 text-base\",\n        icon: \"h-10 w-10 rounded-full\",\n        wide: \"h-10 px-8 py-2 w-full\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,QAAQ;YACR,SAAS;YACT,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/charity_info/charity_info/charity-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/charity_info/charity_info/charity-nextjs/src/app/page.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b bg-card\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-primary\">Charity Info</h1>\n            <nav className=\"hidden md:flex space-x-6\">\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">Home</a>\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">About</a>\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">News</a>\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">Contact</a>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-4xl font-bold text-foreground mb-4\">\n            Welcome to Charity Information Website\n          </h2>\n          <p className=\"text-xl text-muted-foreground mb-8\">\n            Supporting communities through charitable work and transparency\n          </p>\n          <div className=\"flex gap-4 justify-center\">\n            <Button variant=\"default\" size=\"lg\">\n              Learn More\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              Get Involved\n            </Button>\n          </div>\n        </div>\n\n        {/* Feature Cards */}\n        <div className=\"grid md:grid-cols-3 gap-6 mb-12\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Our Mission</CardTitle>\n              <CardDescription>\n                Dedicated to making a positive impact in communities worldwide\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                We work tirelessly to support those in need through various charitable initiatives and programs.\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Transparency</CardTitle>\n              <CardDescription>\n                Open and honest about how donations are used\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                Every donation is tracked and reported to ensure maximum impact and accountability.\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Community</CardTitle>\n              <CardDescription>\n                Building stronger communities together\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                Join our community of volunteers and supporters making a difference every day.\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Migration Status */}\n        <Card className=\"bg-primary/5 border-primary/20\">\n          <CardHeader>\n            <CardTitle className=\"text-primary\">🚀 Next.js Migration in Progress</CardTitle>\n            <CardDescription>\n              Successfully migrated to Next.js with improved performance and SEO\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-600\">✅</span>\n                <span className=\"text-sm\">Next.js project setup complete</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-600\">✅</span>\n                <span className=\"text-sm\">UI components migrated</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-600\">✅</span>\n                <span className=\"text-sm\">Tailwind CSS configured</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-yellow-600\">🔄</span>\n                <span className=\"text-sm\">Page migration in progress</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t bg-card mt-12\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"text-center text-muted-foreground\">\n            <p>&copy; 2024 Charity Information Website. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;kDAC3D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;kDAC3D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;kDAC3D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAGxD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;kDAGpC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;kCAOxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAMjD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAMjD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAe;;;;;;kDACpC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}