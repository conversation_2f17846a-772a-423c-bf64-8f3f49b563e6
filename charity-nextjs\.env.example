# Next.js Environment Variables Example
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_SOCKET_URL=http://localhost:5000

# API Configuration
API_URL=http://localhost:5000

# Database (if needed for API routes)
MONGODB_URI=mongodb://localhost:27017/charity_info

# JWT Secret (if handling auth in Next.js)
JWT_SECRET=your-jwt-secret-key-here

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=5242880
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Development Settings
NODE_ENV=development
