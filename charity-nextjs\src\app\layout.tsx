import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Charity Information Website",
  description: "Supporting communities through charitable work and transparency",
  keywords: ["charity", "donation", "community", "help", "support"],
  authors: [{ name: "Charity Info Team" }],
  openGraph: {
    title: "Charity Information Website",
    description: "Supporting communities through charitable work and transparency",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Charity Information Website",
    description: "Supporting communities through charitable work and transparency",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div className="flex flex-col min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
