{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/charity_info/charity_info/charity-nextjs/src/app/page.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b bg-card\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-primary\">Charity Info</h1>\n            <nav className=\"hidden md:flex space-x-6\">\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">Home</a>\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">About</a>\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">News</a>\n              <a href=\"#\" className=\"text-foreground hover:text-primary\">Contact</a>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-4xl font-bold text-foreground mb-4\">\n            Welcome to Charity Information Website\n          </h2>\n          <p className=\"text-xl text-muted-foreground mb-8\">\n            Supporting communities through charitable work and transparency\n          </p>\n          <div className=\"flex gap-4 justify-center\">\n            <Button variant=\"default\" size=\"lg\">\n              Learn More\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              Get Involved\n            </Button>\n          </div>\n        </div>\n\n        {/* Feature Cards */}\n        <div className=\"grid md:grid-cols-3 gap-6 mb-12\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Our Mission</CardTitle>\n              <CardDescription>\n                Dedicated to making a positive impact in communities worldwide\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                We work tirelessly to support those in need through various charitable initiatives and programs.\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Transparency</CardTitle>\n              <CardDescription>\n                Open and honest about how donations are used\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                Every donation is tracked and reported to ensure maximum impact and accountability.\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Community</CardTitle>\n              <CardDescription>\n                Building stronger communities together\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                Join our community of volunteers and supporters making a difference every day.\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Migration Status */}\n        <Card className=\"bg-primary/5 border-primary/20\">\n          <CardHeader>\n            <CardTitle className=\"text-primary\">🚀 Next.js Migration in Progress</CardTitle>\n            <CardDescription>\n              Successfully migrated to Next.js with improved performance and SEO\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-600\">✅</span>\n                <span className=\"text-sm\">Next.js project setup complete</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-600\">✅</span>\n                <span className=\"text-sm\">UI components migrated</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-600\">✅</span>\n                <span className=\"text-sm\">Tailwind CSS configured</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-yellow-600\">🔄</span>\n                <span className=\"text-sm\">Page migration in progress</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t bg-card mt-12\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"text-center text-muted-foreground\">\n            <p>&copy; 2024 Charity Information Website. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;kDAC3D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;kDAC3D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;kDAC3D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAGxD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,SAAQ;wCAAU,MAAK;kDAAK;;;;;;kDAGpC,8OAAC;wCAAO,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;kCAOxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;;0DACC,8OAAC;0DAAU;;;;;;0DACX,8OAAC;0DAAgB;;;;;;;;;;;;kDAInB,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;;kDACC,8OAAC;;0DACC,8OAAC;0DAAU;;;;;;0DACX,8OAAC;0DAAgB;;;;;;;;;;;;kDAInB,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;;kDACC,8OAAC;;0DACC,8OAAC;0DAAU;;;;;;0DACX,8OAAC;0DAAgB;;;;;;;;;;;;kDAInB,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,8OAAC;wBAAK,WAAU;;0CACd,8OAAC;;kDACC,8OAAC;wCAAU,WAAU;kDAAe;;;;;;kDACpC,8OAAC;kDAAgB;;;;;;;;;;;;0CAInB,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/charity_info/charity_info/charity-nextjs/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/charity_info/charity_info/charity-nextjs/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}